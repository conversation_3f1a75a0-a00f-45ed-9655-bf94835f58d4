"""
Java Spring Boot Target Plugin implementation.
Refactored for clean separation of concerns with minimal coordination logic.
"""
import logging
import os
import time
from typing import Dict, Any, List, Optional

from src.platform.interfaces.target_plugin import (
    TargetPlugin, CodeGenerator, ProjectManager, DocumentationGenerator
)
from src.platform.agents.base_agent import AgentInput
from src.platform.tools.knowledge_database import KnowledgeDatabase
from src.platform.tools.code_generator_tools import CodeGeneratorTools
from src.plugins.targets.java_spring.agents.code_generator import JavaCodeGenerator
from src.plugins.targets.java_spring.agents.data_class_generator import JavaDataClassGenerator
from src.plugins.targets.java_spring.agents.repository_generator import JavaRepositoryGenerator
from src.plugins.targets.java_spring.tools.project_manager.core import JavaProjectManager
from src.plugins.targets.java_spring.tools.documentation_generator import JavaSpringDocumentationGenerator
from src.plugins.targets.java_spring.tools.dependency_analyzer import JavaDependencyAnalyzer
from src.plugins.targets.java_spring.tools.state_manager import JavaStateManager
from src.plugins.targets.java_spring.tools.database_operation_detector import DatabaseOperationDetector
from src.plugins.targets.java_spring.tools.performance_tracker import JavaPerformanceTracker
from src.plugins.targets.java_spring.tools.react_coordinator import JavaReactCoordinator
import llm_settings


class JavaSpringCodeGenerator(CodeGenerator):
    """
    Lightweight coordinator for Java Spring Boot code generation.
    Delegates actual work to specialized classes while maintaining the original React agent behavior.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.knowledge_db = KnowledgeDatabase()

        # Initialize tools early so they're available for all operations
        self.tools = CodeGeneratorTools(self.knowledge_db, "./out")
        self.tools.set_logger(self.logger)

        # Initialize specialized tool classes
        self.dependency_analyzer = JavaDependencyAnalyzer()
        self.state_manager = JavaStateManager()
        self.performance_tracker = JavaPerformanceTracker()
        self.react_coordinator = JavaReactCoordinator()
        self.java_project_manager = JavaProjectManager()
        self.java_code_generator = JavaCodeGenerator(self.knowledge_db)
        self.java_data_class_generator = JavaDataClassGenerator(self.knowledge_db)
        self.java_repository_generator = JavaRepositoryGenerator(self.knowledge_db)
        self.database_operation_detector = DatabaseOperationDetector()

        # Configuration
        self.max_context_tokens = 100000  # Adjust based on your LLM
        self.context_threshold = 0.8  # Start chunking at 80% of max tokens

        self.logger.info("JavaSpringCodeGenerator initialized with delegation architecture")
        self.logger.debug("Delegating to: JavaDependencyAnalyzer, JavaStateManager, JavaPerformanceTracker, JavaReactCoordinator, JavaCodeGenerator, JavaProjectManager")
        self.logger.debug(f"Max context tokens: {self.max_context_tokens}, threshold: {self.context_threshold}")

    def set_up(self, config: dict) -> None:
        """
        Set up the agent with configuration

        Args:
            config: Configuration dictionary
        """
        self.max_context_tokens = config.get('max_context_tokens', 100000)
        self.context_threshold = config.get('context_threshold', 0.8)

    def generate_code(self, source_analysis: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main processing method that orchestrates the Java code generation.
        This is a React agent that can analyze, plan, and execute code generation
        in multiple iterations if needed.

        Args:
            source_analysis: Source analysis data containing working directory and knowledge base
            config: Configuration dictionary

        Returns:
            Dict[str, Any]: Code generation results
        """
        start_time = self.performance_tracker.start_processing()

        self.logger.info("Starting Java Spring Boot code generation process")
        self.logger.info(f"Processing source analysis with {len(source_analysis.get('knowledge_base', {}))} knowledge base entries")
        self.logger.debug(f"Configuration: {config}")

        try:
            # Convert source_analysis to AgentInput format
            working_directory = config.get("working_directory", "./out")
            input_data = AgentInput(
                working_directory=working_directory,
                knowledge_base=source_analysis.get("knowledge_base", {})
            )

            # Update tools with correct working directory
            self.tools = CodeGeneratorTools(self.knowledge_db, input_data.working_directory)
            self.tools.set_logger(self.logger)

            # Create or validate Java project directory
            java_project_dir = os.path.join(input_data.working_directory, "java_project")
            delegation_start = time.time()
            self.java_project_manager.ensure_project_structure(java_project_dir)
            self.performance_tracker.log_delegation_performance("ensure_project_structure", delegation_start)

            # Initialize or load generation state
            delegation_start = time.time()
            generation_state = self.state_manager.load_or_initialize_generation_state(input_data.working_directory)
            self.performance_tracker.log_delegation_performance("load_or_initialize_generation_state", delegation_start)

            # Start React loop
            max_iterations = 50  # Prevent infinite loops
            iteration = 0

            while self.react_coordinator.should_continue_generation(generation_state, iteration, max_iterations):
                iteration += 1
                self.react_coordinator.log_iteration_start(iteration, generation_state)

                # Analyze current state and decide next action
                delegation_start = time.time()
                action = self.react_coordinator.analyze_and_decide_action(generation_state, input_data)
                self.performance_tracker.log_delegation_performance("analyze_and_decide_action", delegation_start)

                # Execute the decided action
                delegation_start = time.time()
                result = self._execute_action(action, generation_state, input_data)
                self.performance_tracker.log_delegation_performance("execute_action", delegation_start, result.get("success", True))

                # Check if there was a critical error that should stop execution
                if not self.react_coordinator.validate_action_result(action, result):
                    total_time = self.performance_tracker.end_processing(start_time)
                    return self.react_coordinator.create_critical_error_result(result, total_time)

                # Update generation state
                delegation_start = time.time()
                self.state_manager.update_generation_state(generation_state, action, result)
                self.performance_tracker.log_delegation_performance("update_generation_state", delegation_start)

                # Save state after each iteration
                delegation_start = time.time()
                self.state_manager.save_generation_state(generation_state, input_data.working_directory)
                self.performance_tracker.log_delegation_performance("save_generation_state", delegation_start)

                # Check if we need to handle context overflow
                if self.state_manager.should_restart_due_to_context():
                    self.logger.info("Context size approaching limit, will continue in next iteration")
                    break

            # Final state check
            total_time = self.performance_tracker.end_processing(start_time)

            # Log performance metrics
            self.performance_tracker.log_performance_metrics()

            # Log generation summary
            self.react_coordinator.log_generation_summary(generation_state, total_time, iteration)

            if self.state_manager.is_generation_complete(generation_state):
                return self.react_coordinator.create_success_result(generation_state, java_project_dir, total_time)
            else:
                return self.react_coordinator.create_partial_result(generation_state, java_project_dir, total_time, iteration)

        except Exception as e:
            total_time = self.performance_tracker.end_processing(start_time)
            self.logger.exception(f"Error during code generation after {total_time:.2f} seconds: {str(e)}")
            return self.react_coordinator.create_error_result(str(e), total_time)

    def _execute_action(self, action: Dict[str, Any], generation_state: Dict[str, Any], input_data: AgentInput) -> Dict[str, Any]:
        """
        Execute the decided action by delegating to appropriate specialized tools.

        Args:
            action: Action to execute
            generation_state: Current generation state
            input_data: Input data

        Returns:
            Dict[str, Any]: Action result
        """
        action_type = action.get("type")
        self.react_coordinator.log_action_execution(action)

        if action_type == "analyze_dependencies":
            delegation_start = time.time()
            result = self.dependency_analyzer.analyze_dependencies(generation_state, input_data)
            self.performance_tracker.log_delegation_performance("analyze_dependencies", delegation_start, result.get("success", True))
            return result

        elif action_type == "transition_to_data_classes":
            delegation_start = time.time()
            result = self.state_manager.transition_to_data_classes(generation_state)
            self.performance_tracker.log_delegation_performance("transition_to_data_classes", delegation_start)
            return result

        elif action_type == "generate_data_classes":
            return self._generate_data_classes(generation_state, input_data)

        elif action_type == "transition_to_generation":
            delegation_start = time.time()
            result = self.state_manager.transition_to_generation(generation_state)
            self.performance_tracker.log_delegation_performance("transition_to_generation", delegation_start)
            return result

        elif action_type == "generate_chunk":
            return self._generate_chunk_code(action.get("chunk_info"), generation_state, input_data)

        elif action_type == "transition_to_finalization":
            delegation_start = time.time()
            result = self.state_manager.transition_to_finalization(generation_state)
            self.performance_tracker.log_delegation_performance("transition_to_finalization", delegation_start)
            return result

        elif action_type == "finalize_project":
            return self._finalize_project(generation_state, input_data)

        elif action_type == "complete":
            return {"success": True, "completed": True}

        else:
            return {"success": False, "error": f"Unknown action type: {action_type}"}

    def _generate_chunk_code(self, chunk_info: Dict[str, Any], generation_state: Dict[str, Any], input_data: AgentInput) -> Dict[str, Any]:
        """
        Generate Java code for a specific chunk.
        Delegates to JavaCodeGenerator for actual code generation.

        Args:
            chunk_info: Information about the chunk to generate
            generation_state: Current generation state
            input_data: Input data

        Returns:
            Dict[str, Any]: Generation result including mappings
        """
        chunk_start_time = self.performance_tracker.start_chunk_processing()

        program_id = chunk_info["program_id"]
        chunk_name = chunk_info["chunk_name"]

        self.logger.info(f"Starting code generation for chunk {program_id}.{chunk_name}")
        self.logger.debug(f"Chunk info: {chunk_info}")
        self.logger.debug(f"Delegating to JavaCodeGenerator for LLM-based generation")

        try:
            # Get chunk documentation from database
            self.logger.debug(f"Retrieving documentation for {program_id}.{chunk_name}")
            delegation_start = time.time()
            chunk_doc = self.knowledge_db.get_chunk_documentation(program_id, chunk_name)
            self.performance_tracker.log_delegation_performance("get_chunk_documentation", delegation_start, chunk_doc is not None)

            if not chunk_doc:
                error_msg = f"No documentation found for {program_id}.{chunk_name}"
                self.logger.error(error_msg)
                chunk_time = self.performance_tracker.end_chunk_processing_failure(chunk_start_time)
                return {"success": False, "error": error_msg}

            self.logger.debug(f"Found documentation with {len(chunk_doc.get('code', ''))} characters of code")

            # Get dependency information
            self.logger.debug(f"Analyzing dependencies for {program_id}.{chunk_name}")
            delegation_start = time.time()
            dependencies = self.dependency_analyzer.get_chunk_dependencies(chunk_info, generation_state)
            self.performance_tracker.log_delegation_performance("get_chunk_dependencies", delegation_start)
            self.logger.debug(f"Found {len(dependencies.get('dependent_services', []))} dependencies")

            # Delegate to JavaCodeGenerator for actual code generation
            self.logger.info(f"Delegating code generation to JavaCodeGenerator for {program_id}.{chunk_name}")
            delegation_start = time.time()
            java_code = self.java_code_generator.generate_chunk_code(chunk_doc, dependencies, generation_state, self.tools)
            self.performance_tracker.log_delegation_performance("generate_chunk_code", delegation_start, bool(java_code))

            if not java_code:
                error_msg = f"JavaCodeGenerator returned empty code for {program_id}.{chunk_name}"
                self.logger.error(error_msg)
                chunk_time = self.performance_tracker.end_chunk_processing_failure(chunk_start_time)
                return {"success": False, "error": error_msg}

            self.logger.info(f"Generated {len(java_code)} characters of Java code for {program_id}.{chunk_name}")

            # Extract mappings that were saved during generation
            self.logger.debug(f"Retrieving COBOL-Java mappings for {program_id}.{chunk_name}")
            delegation_start = time.time()
            mappings = self.tools.get_language_mappings(program_id, "cobol", "java", chunk_name)
            self.performance_tracker.log_delegation_performance("get_language_mappings", delegation_start)
            self.logger.debug(f"Found {len(mappings)} mappings")

            # Extract class information and save to file
            self.logger.info(f"Saving Java code to file for {program_id}.{chunk_name}")
            java_project_dir = os.path.join(input_data.working_directory, "java_project")
            delegation_start = time.time()
            java_class_info = self.java_project_manager.save_java_code_to_file(java_code, chunk_info, java_project_dir)
            self.performance_tracker.log_delegation_performance("save_java_code_to_file", delegation_start, bool(java_class_info.get("file_path")))

            if java_class_info.get("file_path"):
                self.logger.info(f"Saved Java class to: {java_class_info['file_path']}")
            else:
                self.logger.warning(f"File save may have failed for {program_id}.{chunk_name}")

            # Enhanced validation
            self.logger.debug(f"Validating generated Java code for {program_id}.{chunk_name}")
            delegation_start = time.time()
            validation_result = self.java_code_generator.validate_java_code(java_code, chunk_doc)
            self.performance_tracker.log_delegation_performance("validate_java_code", delegation_start)

            if validation_result.get("valid"):
                self.logger.info(f"Code validation passed for {program_id}.{chunk_name}")
            else:
                self.logger.warning(f"Code validation failed for {program_id}.{chunk_name}: {validation_result.get('errors', [])}")

            # Calculate performance metrics
            chunk_time = self.performance_tracker.end_chunk_processing_success(chunk_start_time)
            performance_metrics = self.performance_tracker.get_chunk_performance_metrics(chunk_time, len(java_code), len(mappings))

            result = {
                "success": True,
                "java_code": java_code,
                "java_class_info": java_class_info,
                "validation": validation_result,
                "mappings": mappings,
                "performance_metrics": performance_metrics
            }

            self.logger.info(f"Successfully completed code generation for {program_id}.{chunk_name} in {chunk_time:.2f} seconds")
            self.logger.debug(f"Generated {len(java_code)} characters of Java code with {len(mappings)} mappings")
            return result

        except Exception as e:
            chunk_time = self.performance_tracker.end_chunk_processing_failure(chunk_start_time)
            error_msg = f"Error generating code for chunk {program_id}.{chunk_name} after {chunk_time:.2f} seconds: {str(e)}"
            self.logger.exception(error_msg)
            return {
                "success": False,
                "error": str(e),
                "performance_metrics": self.performance_tracker.get_failure_performance_metrics(chunk_time)
            }

    def _generate_data_classes(self, generation_state: Dict[str, Any], input_data: AgentInput) -> Dict[str, Any]:
        """
        Generate Java data classes from COBOL copybooks.
        Delegates to JavaDataClassGenerator for actual data class generation.

        Args:
            generation_state: Current generation state
            input_data: Input data

        Returns:
            Dict[str, Any]: Data class generation result
        """
        try:
            self.logger.info("Starting data class generation from COBOL copybooks")

            # Delegate to JavaDataClassGenerator for actual data class generation
            delegation_start = time.time()
            result = self.java_data_class_generator.generate_data_classes_from_copybooks(self.tools)
            self.performance_tracker.log_delegation_performance("generate_data_classes_from_copybooks", delegation_start, result.get("success", True))

            if not result.get("success"):
                error_msg = f"JavaDataClassGenerator failed: {result.get('error', 'Unknown error')}"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg}

            generated_classes = result.get("generated_classes", [])
            mappings = result.get("mappings", {})
            total_copybooks = result.get("total_copybooks_processed", 0)

            self.logger.info(f"Generated {len(generated_classes)} Java data classes from {total_copybooks} copybooks")
            self.logger.info(f"Created {len(mappings)} COBOL-Java field mappings")

            # Save generated classes to files
            java_project_dir = os.path.join(input_data.working_directory, "java_project")
            saved_files = []

            for class_info in generated_classes:
                try:
                    # Extract class name from mappings if available
                    mappings = class_info.get("mappings", {})
                    class_info_mapping = mappings.get("class_info", {})

                    # Use package from mappings or default
                    package_name = "com.generated.cobol.entities" if class_info.get("is_entity") else "com.generated.cobol.model"
                    class_name = class_info_mapping.get("java_class_name", "")
                    java_code = class_info.get("java_code", "")

                    # Skip if no valid class name
                    if not class_name or class_name == "UnknownClass":
                        self.logger.warning(f"Skipping class with invalid name: '{class_name}'")
                        continue

                    if java_code:
                        self.logger.info(f"Saving data class: {class_name} to package: {package_name}")
                        delegation_start = time.time()
                        file_path = self.java_project_manager.save_java_file(java_code, package_name, class_name, java_project_dir)
                        self.performance_tracker.log_delegation_performance("save_java_file", delegation_start, bool(file_path))

                        if file_path:
                            saved_files.append({
                                "class_name": class_name,
                                "file_path": file_path,
                                "is_entity": class_info.get("is_entity", False),
                                "field_count": class_info.get("field_count", 0),
                                "program_id": class_info.get("program_id"),
                                "chunk_name": class_info.get("chunk_name")
                            })
                            self.logger.info(f"Saved data class {class_name} to: {file_path}")
                        else:
                            self.logger.warning(f"Failed to save data class {class_name}")

                except Exception as e:
                    self.logger.error(f"Error saving data class {class_info.get('name', 'unknown')}: {str(e)}")
                    continue

            # Save generated repositories to Java project
            saved_repositories = []
            for repository_info in result.get("repositories", []):
                try:
                    repository_name = repository_info.get("name", "UnknownRepository")
                    java_code = repository_info.get("java_code", "")

                    if not java_code:
                        self.logger.warning(f"No Java code found for repository {repository_name}")
                        continue

                    package_name = "com.generated.cobol.data.repository"
                    delegation_start = time.time()
                    file_path = self.java_project_manager.save_java_file(java_code, package_name, repository_name, java_project_dir)
                    self.performance_tracker.log_delegation_performance("save_repository_file", delegation_start, bool(file_path))

                    if file_path:
                        saved_repositories.append({
                            "repository_name": repository_name,
                            "file_path": file_path,
                            "package": package_name
                        })
                        self.logger.info(f"Saved repository {repository_name} to {file_path}")

                except Exception as e:
                    self.logger.error(f"Error saving repository {repository_info.get('name', 'unknown')}: {str(e)}")
                    continue

            # Save generated services to Java project
            saved_services = []
            for service_info in result.get("services", []):
                try:
                    service_name = service_info.get("name", "UnknownService")
                    java_code = service_info.get("java_code", "")

                    if not java_code:
                        self.logger.warning(f"No Java code found for service {service_name}")
                        continue

                    package_name = "com.generated.cobol.data.service"
                    delegation_start = time.time()
                    file_path = self.java_project_manager.save_java_file(java_code, package_name, service_name, java_project_dir)
                    self.performance_tracker.log_delegation_performance("save_service_file", delegation_start, bool(file_path))

                    if file_path:
                        saved_services.append({
                            "service_name": service_name,
                            "file_path": file_path,
                            "package": package_name
                        })
                        self.logger.info(f"Saved service {service_name} to {file_path}")

                except Exception as e:
                    self.logger.error(f"Error saving service {service_info.get('name', 'unknown')}: {str(e)}")
                    continue

            # Update generation state to mark data classes as generated
            generation_state["data_classes_generated"] = True
            generation_state["generated_data_classes"] = saved_files
            generation_state["generated_repositories"] = saved_repositories
            generation_state["generated_services"] = saved_services
            generation_state["data_class_mappings"] = mappings

            return {
                "success": True,
                "generated_classes": len(saved_files),
                "generated_repositories": len(saved_repositories),
                "generated_services": len(saved_services),
                "saved_files": saved_files,
                "saved_repositories": saved_repositories,
                "saved_services": saved_services,
                "mappings": mappings,
                "total_copybooks_processed": total_copybooks
            }

        except Exception as e:
            error_msg = f"Error generating data classes: {str(e)}"
            self.logger.exception(error_msg)
            return {"success": False, "error": error_msg}

    def _finalize_project(self, generation_state: Dict[str, Any], input_data: AgentInput) -> Dict[str, Any]:
        """
        Finalize the Java project structure and configuration.
        Delegates to JavaProjectManager for actual project finalization.

        Args:
            generation_state: Current generation state
            input_data: Input data

        Returns:
            Dict[str, Any]: Finalization result
        """
        try:
            java_project_dir = os.path.join(input_data.working_directory, "java_project")

            # Delegate to JavaProjectManager for project finalization
            delegation_start = time.time()
            self.java_project_manager.finalize_project(java_project_dir, generation_state)
            self.performance_tracker.log_delegation_performance("finalize_project", delegation_start)

            # Delegate to state manager for finalization completion
            delegation_start = time.time()
            result = self.state_manager.mark_finalization_complete(generation_state)
            self.performance_tracker.log_delegation_performance("mark_finalization_complete", delegation_start)

            return result

        except Exception as e:
            self.logger.error(f"Error finalizing project: {str(e)}")
            return {"success": False, "error": str(e)}

    def generate_repositories(self, entities: List[Dict[str, Any]], database_operations: List[Any],
                            generation_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate Spring Data JPA repository interfaces for entities.
        Delegates to JavaRepositoryGenerator for actual repository generation.

        Args:
            entities: List of entity information
            database_operations: List of detected database operations
            generation_state: Current generation state

        Returns:
            List[Dict[str, Any]]: List of generated repository information
        """
        try:
            self.logger.info(f"Starting repository generation for {len(entities)} entities")

            # Delegate to JavaRepositoryGenerator for actual repository generation
            delegation_start = time.time()
            repositories = self.java_repository_generator.generate_repositories_for_entities(
                entities, database_operations, generation_state, self.tools
            )
            self.performance_tracker.log_delegation_performance("generate_repositories_for_entities", delegation_start, bool(repositories))

            self.logger.info(f"Generated {len(repositories)} repository interfaces")
            return repositories

        except Exception as e:
            error_msg = f"Error generating repositories: {str(e)}"
            self.logger.exception(error_msg)
            return []

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get current performance metrics by delegating to performance tracker.

        Returns:
            Dict[str, Any]: Performance metrics
        """
        return self.performance_tracker.get_performance_metrics()


class JavaSpringPlugin(TargetPlugin):
    """Java Spring Boot target plugin."""

    def __init__(self):
        super().__init__("java_spring", "1.0.0")
        self._code_generator = JavaSpringCodeGenerator()
        self._project_manager = JavaProjectManager()
        self._documentation_generator = JavaSpringDocumentationGenerator()

    def get_name(self) -> str:
        return "java_spring"

    def get_version(self) -> str:
        return "1.0.0"

    def get_description(self) -> str:
        return "Java Spring Boot code generation and project management"

    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """Initialize the Java Spring plugin."""
        return True

    def cleanup(self) -> None:
        """Clean up Java Spring plugin resources."""
        pass

    def get_target_name(self) -> str:
        return "java_spring"

    def get_supported_languages(self) -> List[str]:
        return ["cobol", "rpg", "assembly", "jcl"]

    def get_code_generator(self) -> Optional[CodeGenerator]:
        return self._code_generator

    def get_project_manager(self) -> Optional[ProjectManager]:
        return self._project_manager

    def get_documentation_generator(self) -> Optional[DocumentationGenerator]:
        return self._documentation_generator

    def can_handle_language(self, language: str) -> bool:
        """Check if this plugin can handle the given source language."""
        return language in self.get_supported_languages()

    def validate_against_source(self, source_code: str, target_code: str, source_language: str,
                               program_id: str, chunk_name: str) -> Dict[str, Any]:
        """
        Validate generated Java code against source code.
        Delegates to JavaCodeGenerator for validation logic.

        Args:
            source_code: Original source code
            target_code: Generated Java code
            source_language: Source language (e.g., 'cobol')
            program_id: Program ID for context
            chunk_name: Chunk name for context

        Returns:
            Dict[str, Any]: Validation results
        """
        # Delegate to code generator for validation
        return self._code_generator.java_code_generator.validate_java_code(
            target_code,
            {
                "program_id": program_id,
                "chunk_name": chunk_name,
                "source_code": source_code,
                "source_language": source_language
            }
        )
