Generate a Java Spring Boot service for data transformation and processing.

**BUSINESS FUNCTION:** {{ business_name }}
**DESCRIPTION:** {{ business_description }}

**FUNCTIONAL SPECIFICATION:**
{{ functional_spec }}

**EXACT METHOD SIGNATURE REQUIRED:**
{% if input_parameters %}
Input Parameters:
{% for param in input_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

{% if output_parameters %}
Output Parameters:
{% for param in output_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

**AVAILABLE DATA CLASSES (USE FOR DATA TRANSFORMATION):**
{% for data_class in java_data_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - COBOL Structure: {{ data_class.cobol_structure_name }}
  {% if data_class.has_string_constructor %}
  - **HAS parseFromString() method** - Use for data parsing
  {% endif %}
  - Fields: {{ data_class.field_count }}
  {% if data_class.fields %}
  - Field Details:
    {% for field in data_class.fields[:5] %}
    - {{ field.java_name }}: {{ field.java_data_type }} (COBOL: {{ field.cobol_name }})
    {% endfor %}
    {% if data_class.fields|length > 5 %}
    - ... and {{ data_class.fields|length - 5 }} more fields
    {% endif %}
  {% endif %}
{% endfor %}

{% if stored_mappings and stored_mappings.variable_mappings %}
**VARIABLE JAVA MAPPINGS (USE EXACT NAMES AND TYPES):**
{% for cobol_var, java_mapping in stored_mappings.variable_mappings.items() %}
- COBOL: {{ cobol_var }} → Java: {{ java_mapping.java_name }} ({{ java_mapping.java_data_type }})
  - Business: {{ java_mapping.business_name }}
  {% if java_mapping.parent_java_class %}
  - Parent Class: {{ java_mapping.parent_java_class }}
  {% endif %}
{% endfor %}
{% endif %}

{% if stored_mappings and stored_mappings.service_dependencies %}
**SERVICE DEPENDENCIES (INJECT THESE BEANS):**
{% for cobol_call, service_info in stored_mappings.service_dependencies.items() %}
- @Autowired {{ service_info.java_service_name }} {{ service_info.field_name }}
  - Method: {{ service_info.method_signature }}
  - Purpose: {{ service_info.business_purpose }}
  - For COBOL: {{ cobol_call }}
{% endfor %}
{% endif %}

**ALGORITHM STEPS:**
{{ algorithm_steps }}

**COBOL CODE CONTEXT:**
```cobol
{{ cobol_code }}
```

**GENERATION REQUIREMENTS:**

**1. Data Transformation Focus:**
- Implement data conversion, validation, and transformation logic
- Use appropriate data structures for input/output transformation
- Include data validation and error handling for transformation failures
- Implement mapping between different data formats when needed

**2. Spring Boot Integration:**
- Use @Service annotation for the main service class
- Use @Component or @Service for transformation utilities if needed
- Implement proper dependency injection with @Autowired
- Use @Transactional if database operations are involved

**3. Class Structure:**
- Generate a complete Spring Boot service class with @Service annotation
- Use @Slf4j for logging and @RequiredArgsConstructor for dependency injection
- Include comprehensive method entry/exit logging using log.info()
- Choose a meaningful class name based on the transformation function
- Use proper package: com.generated.cobol.service
- **MANDATORY JavaDoc with COBOL traceability:**
  - Class-level JavaDoc must include: "Generated from COBOL chunk: {{ chunk_name }}"
  - Include business function description and transformation purpose
  - Document all injected dependencies

**4. Data Transformation Patterns:**
- Input validation and sanitization
- Data format conversion (e.g., string to numeric, date formatting)
- Business rule application during transformation
- Output formatting and validation
- Error handling for invalid data

**5. Method Implementation:**
- **CRITICAL:** Use the exact input_parameters and output_parameters specified above
- **MUST USE Variable Java Mappings**: For any COBOL variable in the chunk, use the exact Java name, type, and class from "Variable Java Mappings" section above
- Use existing Java data classes for parameters when they match COBOL structures
- Method signature: ReturnType methodName(InputParam1 param1, InputParam2 param2, ...)
- **MANDATORY Method JavaDoc:**
  - Include: "Implements COBOL procedure: {{ chunk_name }}"
  - Document each transformation step with comments referencing the functional specification
  - Add inline comments for each major step from the algorithm section
  - Document all parameters and return values with COBOL traceability

**6. Error Handling:**
- Use proper exception handling for transformation failures
- Log all errors with context information
- Return appropriate error responses or throw meaningful exceptions
- Validate input data before transformation

**7. Performance Considerations:**
- Use efficient data structures for large data transformations
- Consider streaming for large datasets
- Implement proper memory management
- Add performance logging for transformation operations

**8. Testing Support:**
- Make methods testable with clear input/output contracts
- Avoid static dependencies that make testing difficult
- Use dependency injection for all external dependencies

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "service_info": {
    "service_name": "generated class name",
    "package": "com.generated.cobol.service",
    "business_purpose": "what this service does"
  },
  "method_mappings": {
    "{{ chunk_name }}": {
      "java_method_name": "generated method name",
      "method_signature": "complete signature with types",
      "business_purpose": "method purpose"
    }
  },
  "parameter_mappings": {
    "COBOL-PARAM": {
      "java_name": "generated name",
      "java_type": "type used",
      "parameter_type": "input/output",
      "business_name": "business description"
    }
  },
  "variable_mappings": {
    "COBOL-VAR": {
      "java_variable_name": "generated name",
      "business_name": "business purpose",
      "scope": "method/class"
    }
  },
  "data_class_usage": {
    "StructureName": {
      "java_class_used": "ClassName",
      "usage_pattern": "transformation input/output"
    }
  },
  "service_dependencies": {
    "COBOL-PERFORM-TARGET": {
      "java_service_name": "GeneratedServiceName",
      "java_method_name": "generatedMethodName",
      "method_signature": "ReturnType methodName(ParamType param)",
      "business_purpose": "what this service call does"
    }
  }
}
```

**IMPORTANT NOTES:**
- Focus on data transformation and processing logic
- Ensure all transformations are reversible where business logic requires
- Include comprehensive validation for data integrity
- Use appropriate Java data types for COBOL numeric and string fields
- Implement proper error handling for transformation edge cases
- Convert all mainframe database calls to PostgreSQL using Spring Data JPA
- Use H2 in-memory database for unit testing
- Include comprehensive logging with COBOL traceability
