"""
LLM-driven Java Data Class Generator for COBOL Copybooks.
All naming decisions delegated to LLM through templates.
"""
import os
import json
import logging
import re
from typing import Dict, List, Any, Optional
from jinja2 import Environment, FileSystemLoader

from llm_settings import invoke_llm


class JavaDataClassGenerator:
    """
    LLM-driven generation of Java data classes from COBOL copybooks.
    All intelligence resides in LLM through comprehensive templates.
    """

    def __init__(self, knowledge_db=None):
        """Initialize the Java data class generator."""
        self.knowledge_db = knowledge_db
        self.logger = logging.getLogger(__name__)

        # Initialize template environment
        template_dirs = [
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'context'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'generation', 'data_classes'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'generation', 'services'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'responses'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'system_prompts')
        ]
        self.template_env = Environment(loader=FileSystemLoader(template_dirs))
        self.logger.debug(f"Initialized template environment with directories: {template_dirs}")

    def generate_data_classes_from_copybooks(self, tools=None) -> Dict[str, Any]:
        """
        Generate Java data classes from all COBOL copybooks in the database.
        Pure orchestration - all logic in templates and LLM.
        """
        try:
            self.logger.info("Starting data class generation from COBOL copybooks")

            # Get copybooks from database
            copybooks = self._get_copybooks_from_database()
            if not copybooks:
                self.logger.warning("No copybooks found in database")
                return {"success": True, "generated_classes": [], "mappings": {}, "entities": [], "repositories": [], "services": []}

            self.logger.info(f"Found {len(copybooks)} copybooks to process")

            generated_classes = []
            all_mappings = {}
            entity_classes = []
            generated_repositories = []
            generated_services = []

            # Process each copybook
            for copybook in copybooks:
                try:
                    result = self._process_copybook(copybook, tools)
                    generated_classes.extend(result.get("classes", []))
                    all_mappings.update(result.get("mappings", {}))

                    # Collect entity classes for repository generation
                    for class_info in result.get("classes", []):
                        if class_info.get("is_entity", False):
                            entity_classes.append(class_info)

                except Exception as e:
                    self.logger.error(f"Error processing copybook {copybook.get('chunk_name', 'unknown')}: {str(e)}")
                    continue

            self.logger.info(f"Successfully generated {len(generated_classes)} Java data classes")

            # Initialize repositories and services lists
            generated_repositories = []
            generated_services = []

            # Generate repositories and services for entity classes
            if entity_classes and tools:
                self.logger.info(f"Generating repositories and services for {len(entity_classes)} entity classes")

                # Generate repositories
                repositories_result = self._generate_repositories_for_entities(entity_classes, tools)
                generated_repositories = repositories_result.get("repositories", [])

                # Generate data transformation services
                services_result = self._generate_data_transformation_services(generated_classes, tools)
                generated_services = services_result.get("services", [])

            return {
                "success": True,
                "generated_classes": generated_classes,
                "mappings": all_mappings,
                "total_copybooks_processed": len(copybooks),
                "entities": entity_classes,
                "repositories": generated_repositories,
                "services": generated_services
            }

        except Exception as e:
            self.logger.error(f"Error generating data classes from copybooks: {str(e)}")
            return {"success": False, "error": str(e)}

    def _get_copybooks_from_database(self) -> List[Dict[str, Any]]:
        """Query the database for all copybooks."""
        if not self.knowledge_db:
            return []

        try:
            copybook_types = ["DATA_DIVISION_FILE_SECTION", "DATA_DIVISION_WS_SECTION"]
            all_copybooks = []

            for chunk_type in copybook_types:
                copybooks = self.knowledge_db.get_chunks_by_type(chunk_type)
                all_copybooks.extend(copybooks)

            return all_copybooks

        except Exception as e:
            self.logger.error(f"Error querying copybooks: {str(e)}")
            return []

    def _process_copybook(self, copybook: Dict[str, Any], tools=None) -> Dict[str, Any]:
        """Process a single copybook."""
        program_id = copybook.get("program_id")
        chunk_name = copybook.get("chunk_name", "")
        cobol_code = copybook.get("code", "")

        if not program_id or not cobol_code:
            return {"classes": [], "mappings": {}}

        self.logger.info(f"Processing copybook: {program_id}.{chunk_name}")

        # Extract 01-level structures
        structures = self._extract_01_level_structures(cobol_code)
        if not structures:
            self.logger.warning(f"No 01-level structures found in {chunk_name}")
            return {"classes": [], "mappings": {}}

        generated_classes = []
        mappings = {}

        # Process each structure with hierarchy-aware chunking
        for structure in structures:
            try:
                # Check if structure needs chunking
                structure_chunks = self._chunk_structure_by_hierarchy(structure)

                for chunk in structure_chunks:
                    # Build complete context
                    context = self._build_structure_context(chunk, copybook, program_id)

                    # Add hierarchy information to context
                    context['hierarchy_info'] = chunk.get('hierarchy', {})
                    context['is_partial_structure'] = chunk.get('is_partial', False)
                    context['parent_structure_name'] = chunk.get('parent_structure')

                    # Generate Java class
                    result = self._generate_java_class(context, tools)
                    if result.get("java_code"):
                        generated_classes.append(result)
                        mappings.update(result.get("mappings", {}))

            except Exception as e:
                self.logger.error(f"Error processing structure {structure.get('name', 'unknown')}: {str(e)}")
                continue

        return {"classes": generated_classes, "mappings": mappings}

    def _extract_01_level_structures(self, cobol_code: str) -> List[Dict[str, Any]]:
        """Extract 01-level structures from COBOL code with full hierarchy preservation."""
        structures = []
        lines = cobol_code.split('\n')
        current_structure = None
        current_fields = []
        hierarchy_stack = []  # Stack to track parent-child relationships

        for line in lines:
            clean_line = line.strip()
            if not clean_line or clean_line.startswith('*'):
                continue

            # Match field definition with more comprehensive pattern
            field_match = re.match(r'^\s*(\d{2})\s+([A-Z0-9-]+|FILLER)', clean_line, re.IGNORECASE)
            if field_match:
                level = int(field_match.group(1))
                field_name = field_match.group(2).upper()

                if level == 1:
                    # Save previous structure with hierarchy
                    if current_structure and len(current_fields) > 1:
                        structures.append({
                            "name": current_structure,
                            "fields": current_fields,
                            "hierarchy": self._build_hierarchy_tree(current_fields)
                        })

                    # Start new structure
                    current_structure = field_name
                    current_fields = [{
                        "name": field_name,
                        "level": level,
                        "raw_definition": clean_line,
                        "parent": None,
                        "children": []
                    }]
                    hierarchy_stack = [{"name": field_name, "level": level, "index": 0}]

                elif current_structure:
                    # Update hierarchy stack - remove items with level >= current level
                    while hierarchy_stack and hierarchy_stack[-1]["level"] >= level:
                        hierarchy_stack.pop()

                    # Determine parent
                    parent_name = hierarchy_stack[-1]["name"] if hierarchy_stack else None

                    field_info = {
                        "name": field_name,
                        "level": level,
                        "raw_definition": clean_line,
                        "parent": parent_name,
                        "children": []
                    }

                    current_fields.append(field_info)

                    # Add to hierarchy stack
                    hierarchy_stack.append({
                        "name": field_name,
                        "level": level,
                        "index": len(current_fields) - 1
                    })

        # Save last structure with hierarchy
        if current_structure and len(current_fields) > 1:
            structures.append({
                "name": current_structure,
                "fields": current_fields,
                "hierarchy": self._build_hierarchy_tree(current_fields)
            })

        return structures

    def _build_hierarchy_tree(self, fields: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build a hierarchical tree structure from flat field list."""
        if not fields:
            return {}

        # Create a map for quick lookup
        field_map = {field["name"]: field for field in fields}

        # Build parent-child relationships
        for field in fields:
            if field["parent"] and field["parent"] in field_map:
                parent_field = field_map[field["parent"]]
                parent_field["children"].append(field["name"])

        # Return the root (01-level) as the tree root
        root_field = fields[0]  # First field is always 01-level
        return {
            "root": root_field["name"],
            "levels": self._get_level_groups(fields),
            "max_depth": max(field["level"] for field in fields),
            "field_count": len(fields)
        }

    def _get_level_groups(self, fields: List[Dict[str, Any]]) -> Dict[int, List[str]]:
        """Group fields by their COBOL level numbers."""
        level_groups = {}
        for field in fields:
            level = field["level"]
            if level not in level_groups:
                level_groups[level] = []
            level_groups[level].append(field["name"])
        return level_groups

    def _should_chunk_structure(self, structure: Dict[str, Any]) -> bool:
        """Determine if a structure should be chunked based on size and hierarchy."""
        field_count = len(structure.get("fields", []))

        # Don't chunk if structure is small
        if field_count <= 40:
            return False

        # Check if we can chunk at natural hierarchy boundaries
        hierarchy = structure.get("hierarchy", {})
        levels = hierarchy.get("levels", {})

        # If we have multiple level groups, we can chunk
        return len(levels) > 2  # More than just 01 and one other level

    def _chunk_structure_by_hierarchy(self, structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk a large structure respecting COBOL level boundaries."""
        fields = structure.get("fields", [])
        hierarchy = structure.get("hierarchy", {})
        levels = hierarchy.get("levels", {})

        if not self._should_chunk_structure(structure):
            return [structure]  # Return as single chunk

        chunks = []
        current_chunk_fields = [fields[0]]  # Always include 01-level
        current_field_count = 1

        # Group by major level boundaries (05, 10, etc.)
        major_levels = sorted([level for level in levels.keys() if level > 1 and level <= 15])

        for field in fields[1:]:  # Skip 01-level (already included)
            current_chunk_fields.append(field)
            current_field_count += 1

            # Check if we should create a chunk at this boundary
            if (current_field_count >= 40 and
                field["level"] in major_levels and
                self._is_natural_boundary(field, fields)):

                # Create chunk
                chunk = {
                    "name": f"{structure['name']}_PART_{len(chunks) + 1}",
                    "fields": current_chunk_fields.copy(),
                    "hierarchy": self._build_hierarchy_tree(current_chunk_fields),
                    "is_partial": True,
                    "parent_structure": structure["name"]
                }
                chunks.append(chunk)

                # Start new chunk with 01-level
                current_chunk_fields = [fields[0]]
                current_field_count = 1

        # Add remaining fields as final chunk
        if len(current_chunk_fields) > 1:
            chunk = {
                "name": f"{structure['name']}_PART_{len(chunks) + 1}",
                "fields": current_chunk_fields,
                "hierarchy": self._build_hierarchy_tree(current_chunk_fields),
                "is_partial": True,
                "parent_structure": structure["name"]
            }
            chunks.append(chunk)

        return chunks if chunks else [structure]

    def _is_natural_boundary(self, field: Dict[str, Any], all_fields: List[Dict[str, Any]]) -> bool:
        """Check if this field represents a natural chunking boundary."""
        # A natural boundary is typically a major level (05, 10) that starts a new group
        # and doesn't have children at the same level immediately following
        field_index = next(i for i, f in enumerate(all_fields) if f["name"] == field["name"])

        # Check if next field (if exists) is at a different major level
        if field_index + 1 < len(all_fields):
            next_field = all_fields[field_index + 1]
            return next_field["level"] <= field["level"]

        return True  # End of structure is always a boundary

    def _build_structure_context(self, structure: Dict[str, Any], copybook: Dict[str, Any], program_id: str) -> Dict[str, Any]:
        """Build complete context for structure generation."""
        context = {
            'structure': structure,
            'program_id': program_id,
            'copybook': copybook,
            'field_count': len(structure.get('fields', []))
        }

        # Add hierarchy information
        hierarchy = structure.get('hierarchy', {})
        if hierarchy:
            context['hierarchy_info'] = hierarchy
            context['has_complex_hierarchy'] = len(hierarchy.get('levels', {})) > 2
            context['max_hierarchy_depth'] = hierarchy.get('max_depth', 1)

            # Analyze hierarchy patterns for template selection
            context['hierarchy_analysis'] = self._analyze_hierarchy_patterns(structure)

        if self.knowledge_db:
            # Get business mappings for fields in this structure
            field_names = [f['name'] for f in structure.get('fields', [])]
            context['business_mappings'] = self._get_relevant_business_mappings(program_id, field_names)

            # Get existing mappings for consistency
            context['existing_mappings'] = self._get_relevant_existing_mappings(program_id, field_names)

            # Detect if this structure is used in file operations
            context['file_operations'] = self._detect_structure_file_usage(program_id, structure['name'])

            # Get naming patterns
            context['naming_patterns'] = self._get_naming_patterns(program_id)

        return context

    def _analyze_hierarchy_patterns(self, structure: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze hierarchy patterns to recommend the best Java class design approach."""
        fields = structure.get('fields', [])
        hierarchy = structure.get('hierarchy', {})
        levels = hierarchy.get('levels', {})

        analysis = {
            'recommended_approach': 'field_grouping',  # default
            'complexity_score': 0,
            'group_count': 0,
            'max_children_per_group': 0
        }

        # Count groups (non-elementary items that have children)
        groups = []
        for field in fields:
            if field.get('children'):
                groups.append(field)
                analysis['max_children_per_group'] = max(
                    analysis['max_children_per_group'],
                    len(field['children'])
                )

        analysis['group_count'] = len(groups)

        # Calculate complexity score
        level_count = len(levels)
        max_depth = hierarchy.get('max_depth', 1)
        field_count = len(fields)

        analysis['complexity_score'] = (level_count * 2) + (max_depth * 3) + (field_count * 0.1)

        # Recommend approach based on complexity
        if analysis['complexity_score'] > 20 and max_depth > 10:
            analysis['recommended_approach'] = 'nested_classes'
        elif analysis['complexity_score'] > 10 and analysis['group_count'] > 3:
            analysis['recommended_approach'] = 'composition'
        else:
            analysis['recommended_approach'] = 'field_grouping'

        return analysis

    def _get_relevant_business_mappings(self, program_id: str, field_names: List[str]) -> Dict[str, Dict[str, str]]:
        """Get business mappings for specific fields."""
        if not self.knowledge_db or not field_names:
            return {}

        try:
            all_mappings = self.knowledge_db.get_business_name_mappings(program_id)
            return {k: v for k, v in all_mappings.items() if k in field_names}
        except Exception as e:
            self.logger.error(f"Error getting business mappings: {str(e)}")
            return {}

    def _get_relevant_existing_mappings(self, program_id: str, field_names: List[str]) -> Dict[str, str]:
        """Get existing mappings for specific fields."""
        if not self.knowledge_db or not field_names:
            return {}

        try:
            all_mappings = self.knowledge_db.get_cobol_java_mappings(program_id)
            return {k: v for k, v in all_mappings.items() if k in field_names}
        except Exception as e:
            self.logger.error(f"Error getting existing mappings: {str(e)}")
            return {}

    def _detect_structure_file_usage(self, program_id: str, structure_name: str) -> Dict[str, Any]:
        """Detect if structure is used in file operations in other chunks."""
        if not self.knowledge_db:
            return {'used_in_file_operations': False}

        try:
            # Get all chunks for this program
            chunks = self.knowledge_db.get_chunks_by_program(program_id)

            file_usage = []
            for chunk in chunks:
                # Skip copybook chunks
                if chunk.get('chunk_type') in ['DATA_DIVISION_FILE_SECTION', 'DATA_DIVISION_WS_SECTION']:
                    continue

                # Check functional spec for file operations with this structure
                functional_spec = chunk.get('functional_spec', '') or ''
                if structure_name in functional_spec:
                    # Look for file operation patterns
                    patterns = [
                        f'READ.*INTO\\s+{structure_name}',
                        f'WRITE.*FROM\\s+{structure_name}',
                        f'{structure_name}.*FILE'
                    ]

                    for pattern in patterns:
                        if re.search(pattern, functional_spec, re.IGNORECASE):
                            file_usage.append({
                                'chunk_name': chunk.get('chunk_name'),
                                'operation': pattern
                            })

            return {
                'used_in_file_operations': bool(file_usage),
                'file_operations': file_usage,
                'requires_string_constructor': bool(file_usage)
            }

        except Exception as e:
            self.logger.error(f"Error detecting file usage: {str(e)}")
            return {'used_in_file_operations': False}

    def _get_naming_patterns(self, program_id: str) -> List[Dict[str, str]]:
        """Get naming patterns from existing mappings."""
        if not self.knowledge_db:
            return []

        try:
            mappings = self.knowledge_db.get_cobol_java_mappings(program_id)
            patterns = []

            # Analyze patterns
            for cobol_name, java_name in list(mappings.items())[:10]:  # Sample first 10
                patterns.append({
                    'cobol_example': cobol_name,
                    'java_example': java_name
                })

            return patterns

        except Exception as e:
            self.logger.error(f"Error getting naming patterns: {str(e)}")
            return []

    def _generate_java_class(self, context: Dict[str, Any], tools=None) -> Dict[str, Any]:
        """Generate Java class using LLM."""
        try:
            # Select template based on context
            template_name = self._select_data_class_template(context)
            # Render prompts
            system_prompt = self._render_system_prompt('data_class_generation_system.j2', context)
            user_prompt = self._render_user_prompt(template_name, context)

            # Generate with LLM
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = invoke_llm(messages)

            # Extract code and mappings
            java_code = self._extract_java_code(response)
            mappings = self._extract_mappings(response)



            if not java_code:
                self.logger.error(f"No Java code generated for structure {context['structure']['name']}")
                return {}

            # Validate that we have a proper class name
            if not mappings or 'class_info' not in mappings:
                self.logger.error(f"No class info mappings generated for structure {context['structure']['name']}")
                return {}

            class_name = mappings['class_info'].get('java_class_name', '')
            if not class_name or class_name == 'UnknownClass':
                self.logger.error(f"Invalid class name '{class_name}' generated for structure {context['structure']['name']}")
                return {}

            # Save mappings to database
            if tools and mappings:
                self._save_data_class_mappings(tools, context['program_id'], mappings)

            # Extract class name from mappings for proper file saving
            class_name = mappings['class_info'].get('java_class_name', '')

            return {
                "java_code": java_code,
                "mappings": mappings,
                "structure_name": context['structure']['name'],
                "program_id": context['program_id'],
                "name": class_name,  # Add this for plugin compatibility
                "is_entity": mappings['class_info'].get('is_entity', False)
            }

        except Exception as e:
            self.logger.error(f"Error generating Java class: {str(e)}")
            return {}

    def _select_data_class_template(self, context: Dict[str, Any]) -> str:
        """Select appropriate data class template based on structure characteristics."""
        # Check if this structure has hierarchical complexity
        hierarchy_info = context.get('hierarchy_info', {})
        levels = hierarchy_info.get('levels', {})
        max_depth = hierarchy_info.get('max_depth', 1)
        field_count = context.get('field_count', 0)

        # Check for hierarchical indicators
        has_multiple_levels = len(levels) > 1  # More than just 01 level
        has_deep_hierarchy = max_depth > 2     # Deeper than 01-05 levels
        has_many_fields = field_count > 10     # Large structure likely to have hierarchy
        is_partial = context.get('is_partial_structure', False)

        # Check if structure has group fields (fields with children)
        structure = context.get('structure', {})
        fields = structure.get('fields', [])
        has_group_fields = any(field.get('children') for field in fields)

        # Log template selection reasoning
        self.logger.debug(f"Template selection for {structure.get('name', 'unknown')}: "
                         f"levels={len(levels)}, max_depth={max_depth}, field_count={field_count}, "
                         f"has_group_fields={has_group_fields}, is_partial={is_partial}")

        # Use hierarchical template if:
        # 1. Structure has multiple COBOL levels (more than just 01)
        # 2. Structure has depth > 2 levels (01, 05, 10, etc.)
        # 3. Structure is a partial structure (chunked)
        # 4. Structure has group fields with children
        # 5. Large structure with many fields (likely hierarchical)
        if (has_multiple_levels or
            has_deep_hierarchy or
            is_partial or
            has_group_fields or
            (has_many_fields and max_depth > 1)):
            self.logger.info(f"Selected hierarchical template for {structure.get('name', 'unknown')}")
            return 'hierarchical_data_class.j2'
        elif context.get('file_operations', {}).get('requires_string_constructor'):
            self.logger.info(f"Selected file record template for {structure.get('name', 'unknown')}")
            return 'file_record_data_class.j2'
        else:
            self.logger.info(f"Selected standard template for {structure.get('name', 'unknown')}")
            return 'standard_data_class.j2'

    def _render_system_prompt(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render system prompt."""
        template = self.template_env.get_template(template_name)
        return template.render(**context)

    def _render_user_prompt(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render user prompt."""
        template = self.template_env.get_template(template_name)
        return template.render(**context)

    def _extract_java_code(self, response: str) -> str:
        """Extract Java code from response."""
        java_pattern = r'```java\s*(.*?)\s*```'
        match = re.search(java_pattern, response, re.DOTALL | re.IGNORECASE)
        return match.group(1).strip() if match else ""

    def _extract_mappings(self, response: str) -> Dict[str, Any]:
        """Extract mappings from response."""
        json_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_pattern, response, re.DOTALL | re.IGNORECASE)

        if match:
            try:
                return json.loads(match.group(1).strip())
            except json.JSONDecodeError:
                self.logger.error("Failed to parse JSON mappings")

        return {}

    def _save_data_class_mappings(self, tools, program_id: str, mappings: Dict[str, Any]):
        """Save comprehensive data class mappings to database."""
        try:
            # Save class mapping with comprehensive information
            if 'class_info' in mappings:
                class_info = mappings['class_info']
                cobol_structure_name = class_info.get('cobol_structure_name', '')
                java_class_name = class_info.get('java_class_name', '')

                # Prepare comprehensive class mapping info
                class_mapping_info = {
                    'package': class_info.get('package', ''),
                    'business_purpose': class_info.get('business_purpose', ''),
                    'has_string_constructor': class_info.get('has_string_constructor', False),
                    'is_entity_field': False,  # Data classes are not entity fields
                    'java_type': 'class',
                    'mapping_notes': f"Generated Java data class from COBOL structure {cobol_structure_name}",
                    'generation_metadata': class_info
                }

                tools.save_comprehensive_cobol_java_mapping(
                    program_id, cobol_structure_name, cobol_structure_name,
                    java_class_name, 'class', class_mapping_info
                )

            # Save field mappings with comprehensive information including hierarchy
            if 'field_mappings' in mappings:
                structure_name = mappings.get('class_info', {}).get('cobol_structure_name', '')
                hierarchy_mappings = mappings.get('hierarchy_mappings', {})
                class_info = mappings.get('class_info', {})

                for cobol_field, field_info in mappings['field_mappings'].items():
                    java_field_name = field_info.get('java_field_name', '')

                    # Get hierarchy info for this field
                    hierarchy_info = hierarchy_mappings.get(cobol_field, {})

                    # Prepare comprehensive field mapping info with hierarchy
                    field_mapping_info = {
                        'java_type': field_info.get('java_type', 'String'),
                        'business_purpose': field_info.get('business_name', ''),
                        'field_position': field_info.get('field_position', ''),
                        'is_entity_field': False,  # Data class fields are not entity fields
                        'has_string_constructor': False,  # Fields don't have constructors
                        'mapping_notes': f"Generated Java field from COBOL field {cobol_field}",
                        'generation_metadata': field_info,
                        # Hierarchy-specific information
                        'cobol_level': hierarchy_info.get('cobol_level', 1),
                        'parent_field': hierarchy_info.get('parent_field'),
                        'child_fields': hierarchy_info.get('child_fields', []),
                        'hierarchy_path': hierarchy_info.get('hierarchy_path', ''),
                        'hierarchy_approach': class_info.get('hierarchy_approach', 'field_grouping')
                    }

                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, structure_name, cobol_field,
                        java_field_name, 'field', field_mapping_info
                    )

                # Save hierarchy mappings if using nested classes
                if mappings.get('nested_classes'):
                    for nested_class_name, nested_info in mappings['nested_classes'].items():
                        nested_mapping_info = {
                            'java_type': 'nested_class',
                            'business_purpose': nested_info.get('business_purpose', ''),
                            'is_entity_field': False,
                            'has_string_constructor': False,
                            'mapping_notes': f"Generated nested class for COBOL group structure",
                            'generation_metadata': nested_info,
                            'parent_class': class_info.get('java_class_name', ''),
                            'hierarchy_approach': 'nested_classes'
                        }

                        tools.save_comprehensive_cobol_java_mapping(
                            program_id, structure_name, nested_class_name,
                            nested_class_name, 'nested_class', nested_mapping_info
                        )

            # Save constructor information if present
            if 'constructor_info' in mappings:
                constructor_info = mappings['constructor_info']
                structure_name = mappings.get('class_info', {}).get('cobol_structure_name', '')

                constructor_mapping_info = {
                    'java_type': 'constructor',
                    'business_purpose': constructor_info.get('purpose', ''),
                    'has_string_constructor': True,
                    'mapping_notes': f"String parsing constructor for {structure_name}",
                    'generation_metadata': constructor_info
                }

                tools.save_comprehensive_cobol_java_mapping(
                    program_id, structure_name, f"{structure_name}_CONSTRUCTOR",
                    'parseFromString', 'method', constructor_mapping_info
                )

            # Save field mappings to variables table
            self._save_field_mappings_to_variables_table(tools, program_id, mappings)

            self.logger.info(f"Saved comprehensive data class mappings for {program_id}")

        except Exception as e:
            self.logger.error(f"Error saving comprehensive mappings: {str(e)}")

    def _save_field_mappings_to_variables_table(self, tools, program_id: str, mappings: Dict[str, Any]):
        """Save field mappings to the variables table with Java mapping information."""
        try:


            # Check for either field_mappings or hierarchy_mappings
            has_field_mappings = 'field_mappings' in mappings
            has_hierarchy_mappings = 'hierarchy_mappings' in mappings
            has_class_info = 'class_info' in mappings

            if not has_class_info:
                self.logger.warning(f"Missing class_info in mappings")
                return

            if not has_field_mappings and not has_hierarchy_mappings:
                self.logger.warning(f"Missing both field_mappings and hierarchy_mappings in mappings")
                return

            class_info = mappings['class_info']

            # Use field_mappings if available, otherwise convert hierarchy_mappings to field_mappings
            if has_field_mappings:
                field_mappings = mappings['field_mappings']
            else:
                # Convert hierarchy_mappings to field_mappings format
                hierarchy_mappings = mappings['hierarchy_mappings']
                field_mappings = {}

                # Filter out the root structure (level 1) and only include actual fields
                for cobol_name, mapping_info in hierarchy_mappings.items():
                    cobol_level = mapping_info.get('cobol_level', 1)
                    if cobol_level > 1:  # Skip the root structure
                        field_mappings[cobol_name] = {
                            'java_field_name': mapping_info.get('java_field_name', ''),
                            'java_type': mapping_info.get('java_type', 'String'),
                            'business_name': mapping_info.get('business_name', ''),
                            'field_position': mapping_info.get('field_position', '')
                        }

            java_class_name = class_info.get('java_class_name', '')
            package_name = class_info.get('package', 'com.generated.cobol')
            is_entity = class_info.get('is_entity', False)



            # Prepare field mappings for variables table with hierarchy information
            field_mapping_list = []
            hierarchy_mappings = mappings.get('hierarchy_mappings', {})

            for cobol_field_name, field_info in field_mappings.items():
                java_field_name = field_info.get('java_field_name', '')
                java_type = field_info.get('java_type', 'String')
                business_name = field_info.get('business_name', '')
                field_position = field_info.get('field_position', '')

                # Get hierarchy info for this field
                hierarchy_info = hierarchy_mappings.get(cobol_field_name, {})

                # Build full qualified name
                full_qualified_name = f"{package_name}.{java_class_name}.{java_field_name}"

                # Determine annotations based on type and entity status
                annotations = []
                if is_entity:
                    annotations.append('@Column')
                    if 'ID' in cobol_field_name.upper() or cobol_field_name.upper().endswith('-ID'):
                        annotations.append('@Id')

                # Determine parent Java class - for fields, this should be the current Java class
                # since all fields belong to the same class in the field_grouping approach
                parent_java_class = None
                if hierarchy_info.get('parent_field'):
                    # In field_grouping approach, all fields belong to the same class
                    # so parent_java_class should be the current class for nested relationships
                    parent_java_class = java_class_name

                # Enhanced field mapping with hierarchy information
                field_mapping = {
                    'cobol_name': cobol_field_name,
                    'java_name': java_field_name,
                    'java_class': java_class_name,
                    'parent_java_class': parent_java_class,
                    'package_name': package_name,
                    'full_qualified_name': full_qualified_name,
                    'java_data_type': java_type,
                    'java_annotations': annotations,
                    'is_entity_field': is_entity,
                    'mapping_notes': f"Generated Java field from COBOL field {cobol_field_name} (Level {hierarchy_info.get('cobol_level', 1)}) in data class {java_class_name}",
                    # Additional hierarchy information for extended schema
                    'cobol_level': hierarchy_info.get('cobol_level', 1),
                    'hierarchy_path': hierarchy_info.get('hierarchy_path', ''),
                    'parent_cobol_field': hierarchy_info.get('parent_field'),
                    'child_cobol_fields': hierarchy_info.get('child_fields', []),
                    'hierarchy_approach': class_info.get('hierarchy_approach', 'field_grouping')
                }

                field_mapping_list.append(field_mapping)

            # Save to variables table using mapping manager
            if hasattr(tools, 'knowledge_db') and hasattr(tools.knowledge_db, 'mapping_manager'):
                # Save field mappings to variables table
                tools.knowledge_db.mapping_manager.save_java_field_mappings(program_id, field_mapping_list)
                self.logger.info(f"Successfully saved {len(field_mapping_list)} field mappings to variables table for {java_class_name}")
            else:
                self.logger.error("Cannot save field mappings: tools.knowledge_db.mapping_manager not available")

        except Exception as e:
            self.logger.error(f"Error saving field mappings to variables table: {str(e)}")

    def _generate_repositories_for_entities(self, entity_classes: List[Dict[str, Any]], tools) -> Dict[str, Any]:
        """Generate JPA repositories for entity classes."""
        try:
            self.logger.info(f"Generating repositories for {len(entity_classes)} entity classes")

            # Import repository generator
            from src.plugins.targets.java_spring.agents.repository_generator import JavaRepositoryGenerator
            repository_generator = JavaRepositoryGenerator(self.knowledge_db)

            generated_repositories = []

            for entity_info in entity_classes:
                try:
                    # Get database operations for this entity
                    program_id = entity_info.get("program_id", "")
                    entity_name = entity_info.get("name", "")

                    # Detect database operations from functional specs
                    database_operations = self._detect_database_operations_for_entity(program_id, entity_name)

                    # Prepare entity info with correct field names for repository generator
                    repository_entity_info = {
                        **entity_info,
                        "entity_name": entity_name  # Add entity_name field expected by repository generator
                    }

                    # Generate repository
                    repository_result = repository_generator.generate_repository_interface(
                        repository_entity_info, database_operations, {}, tools
                    )

                    if repository_result and repository_result.get("java_code"):
                        generated_repositories.append(repository_result)
                        self.logger.info(f"Generated repository for entity: {entity_name}")

                except Exception as e:
                    self.logger.error(f"Error generating repository for entity {entity_info.get('name', 'unknown')}: {str(e)}")
                    continue

            return {"repositories": generated_repositories}

        except Exception as e:
            self.logger.error(f"Error generating repositories: {str(e)}")
            return {"repositories": []}

    def _generate_data_transformation_services(self, data_classes: List[Dict[str, Any]], tools) -> Dict[str, Any]:
        """Generate data transformation services for data classes."""
        try:
            self.logger.info(f"Generating data transformation services for {len(data_classes)} data classes")

            generated_services = []

            # Group data classes by program for service generation
            classes_by_program = {}
            for class_info in data_classes:
                program_id = class_info.get("program_id", "")
                if program_id not in classes_by_program:
                    classes_by_program[program_id] = []
                classes_by_program[program_id].append(class_info)

            # Generate transformation service for each program
            for program_id, program_classes in classes_by_program.items():
                try:
                    service_result = self._generate_transformation_service_for_program(program_id, program_classes, tools)
                    if service_result and service_result.get("java_code"):
                        generated_services.append(service_result)
                        self.logger.info(f"Generated transformation service for program: {program_id}")

                except Exception as e:
                    self.logger.error(f"Error generating transformation service for program {program_id}: {str(e)}")
                    continue

            return {"services": generated_services}

        except Exception as e:
            self.logger.error(f"Error generating transformation services: {str(e)}")
            return {"services": []}

    def _detect_database_operations_for_entity(self, program_id: str, entity_name: str) -> List[Dict[str, Any]]:
        """Detect database operations for a specific entity."""
        try:
            if not self.knowledge_db:
                return []

            # Get all chunks for this program
            chunks = self.knowledge_db.get_chunks_by_program(program_id)
            database_operations = []

            for chunk in chunks:
                functional_spec = chunk.get('functional_spec', '') or ''
                code = chunk.get('code', '') or ''

                # Look for database operations mentioning this entity
                if entity_name.upper() in functional_spec.upper() or entity_name.upper() in code.upper():
                    # Detect specific database operation patterns
                    operations = self._extract_database_operations_from_text(functional_spec + " " + code, entity_name)
                    database_operations.extend(operations)

            return database_operations

        except Exception as e:
            self.logger.error(f"Error detecting database operations for entity {entity_name}: {str(e)}")
            return []

    def _extract_database_operations_from_text(self, text: str, entity_name: str) -> List[Dict[str, Any]]:
        """Extract database operations from text."""
        operations = []
        text_upper = text.upper()

        # Common database operation patterns
        patterns = {
            'SELECT': ['SELECT', 'GU ', 'GN ', 'READ'],
            'INSERT': ['INSERT', 'ISRT', 'WRITE'],
            'UPDATE': ['UPDATE', 'REPL', 'REWRITE'],
            'DELETE': ['DELETE', 'DLET']
        }

        for operation_type, keywords in patterns.items():
            for keyword in keywords:
                if keyword in text_upper:
                    operations.append({
                        'type': operation_type,
                        'description': f"{operation_type} operation on {entity_name}",
                        'cobol_code': keyword,
                        'fields': [],
                        'conditions': []
                    })

        return operations

    def _generate_transformation_service_for_program(self, program_id: str, data_classes: List[Dict[str, Any]], tools) -> Dict[str, Any]:
        """Generate transformation service for a program's data classes."""
        try:
            # Build context for service generation
            context = {
                'program_id': program_id,
                'java_data_classes': data_classes,
                'chunk_name': f"{program_id}_TRANSFORMATION_SERVICE",
                'business_purpose': f"Data transformation service for {program_id}",
                'service_name': f"{program_id.replace('-', '')}TransformationService"
            }

            # Get functional specs for this program
            if self.knowledge_db:
                chunks = self.knowledge_db.get_chunks_by_program(program_id)
                context['functional_specs'] = [chunk.get('functional_spec', '') for chunk in chunks if chunk.get('functional_spec')]

            # Render transformation service template
            template_name = 'data_transformation_service.j2'
            system_prompt = self._render_system_prompt('service_generation_system.j2', context)
            user_prompt = self._render_user_prompt(template_name, context)

            # Generate service using LLM
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = invoke_llm(messages)

            # Extract code and mappings
            java_code = self._extract_java_code(response)
            mappings = self._extract_mappings(response)

            if not java_code:
                self.logger.error(f"No Java service code generated for program {program_id}")
                return {}

            return {
                "java_code": java_code,
                "mappings": mappings,
                "program_id": program_id,
                "name": context['service_name'],
                "type": "transformation_service"
            }

        except Exception as e:
            self.logger.error(f"Error generating transformation service for program {program_id}: {str(e)}")
            return {}